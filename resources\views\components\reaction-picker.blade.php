@props(['targetId', 'targetType' => 'post', 'currentReaction' => null, 'position' => 'bottom'])

@php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
@endphp

<div class="relative inline-block">
    <!-- Main Reaction Button -->
    <button
        class="reaction-main-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
        data-current-reaction="{{ $currentReactionType }}"
    >
        @if($currentReaction)
            <span class="text-lg">{{ $reactions[$currentReactionType]['emoji'] }}</span>
            <span class="text-sm font-medium {{ $reactions[$currentReactionType]['color'] }}">
                {{ $reactions[$currentReactionType]['label'] }}
            </span>
        @else
            <span class="text-lg">👍</span>
            <span class="text-sm font-medium">Like</span>
        @endif
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-picker absolute {{ $position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2' }} left-0 bg-white rounded-full shadow-lg border border-gray-200 px-2 py-1 flex space-x-1 opacity-0 invisible transition-all duration-200 transform scale-95 z-50"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
    >
        @foreach($reactions as $type => $reaction)
            <button
                class="reaction-option flex flex-col items-center p-2 rounded-lg hover:bg-gray-50 transition-all duration-200 transform hover:scale-110"
                data-reaction-type="{{ $type }}"
                data-target-id="{{ $targetId }}"
                data-target-type="{{ $targetType }}"
                title="{{ $reaction['label'] }}"
            >
                <span class="text-2xl mb-1">{{ $reaction['emoji'] }}</span>
                <span class="text-xs text-gray-600 font-medium">{{ $reaction['label'] }}</span>
            </button>
        @endforeach
    </div>
</div>

<style>
@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-8px); }
    70% { transform: translateY(-4px); }
    90% { transform: translateY(-2px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

.reaction-animation-heartBeat { animation: heartBeat 0.6s ease-in-out; }
.reaction-animation-bounce { animation: bounce 0.6s ease-in-out; }
.reaction-animation-shake { animation: shake 0.6s ease-in-out; }
.reaction-animation-pulse { animation: pulse 0.6s ease-in-out; }
.reaction-animation-fadeIn { animation: fadeIn 0.6s ease-in-out; }
</style>
