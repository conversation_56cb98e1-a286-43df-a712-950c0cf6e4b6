<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('dashboard') }}" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $post->title }}</h1>
                <p class="text-gray-600 mt-1">
                    by {{ $post->user->name }}
                    @if($post->organization)
                        in {{ $post->organization->name }}
                    @endif
                    • {{ $post->published_at->format('M j, Y \a\t g:i A') }}
                </p>
            </div>
        </div>
    </div>

    <!-- Post Content -->
    <div class="max-w-4xl">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Post Header -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    @if($post->organization)
                        <img class="h-16 w-16 rounded-full"
                             src="{{ $post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                             alt="{{ $post->organization->name }}">
                    @else
                        <!-- Debug: {{ $post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'NO AVATAR' }} -->
                        <img class="h-16 w-16 rounded-full"
                             src="{{ $post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ $post->user->name }}">
                    @endif
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h2 class="text-xl font-semibold text-gray-900">
                                {{ $post->organization ? $post->organization->name : $post->user->name }}
                            </h2>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                @if($post->type === 'event') bg-blue-100 text-blue-800
                                @elseif($post->type === 'announcement') bg-yellow-100 text-yellow-800
                                @elseif($post->type === 'financial_report') bg-green-100 text-green-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $post->type)) }}
                            </span>
                        </div>
                        <p class="text-gray-600 mt-1">
                            @if($post->organization)
                                Posted by {{ $post->user->name }} • 
                            @endif
                            {{ $post->published_at->format('M j, Y \a\t g:i A') }}
                        </p>
                    </div>
                    
                    <!-- Post Actions -->
                    @if(auth()->id() === $post->user_id || auth()->user()->isAdmin())
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('posts.edit', $post) }}" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </a>
                            <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('Are you sure you want to delete this post?')" class="text-gray-400 hover:text-red-600">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Post Content -->
            <div class="p-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $post->title }}</h1>
                <div class="prose max-w-none text-gray-700 mb-6">
                    {!! nl2br(e($post->content)) !!}
                </div>

                <!-- Images -->
                @if($post->images && count($post->images) > 0)
                    <div class="mb-6">
                        @if(count($post->images) === 1)
                            <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($post->images[0]) }}"
                                 alt="Post image"
                                 class="w-full max-h-96 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                 onclick="openImageModal({{ json_encode($post->images) }}, 0)">
                        @else
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($post->images as $index => $image)
                                    <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}"
                                         alt="Post image"
                                         class="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal({{ json_encode($post->images) }}, {{ $index }})">
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Facebook Embed -->
                @if($post->facebook_embed_url)
                    <div class="mb-6">
                        <iframe src="{{ $post->facebook_embed_url }}" width="100%" height="400" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true"></iframe>
                    </div>
                @endif
            </div>

            <!-- Post Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="flex items-center space-x-6">
                    @auth
                        <x-reaction-picker
                            :targetId="$post->id"
                            targetType="post"
                            :currentReaction="$post->getUserReaction(auth()->user())"
                        />
                        <span class="text-sm text-gray-500" id="post-reaction-count-{{ $post->id }}">
                            {{ $post->likes->count() ? $post->likes->count() . ' reactions' : '' }}
                        </span>
                    @else
                        <button class="flex items-center space-x-2 text-gray-500 cursor-not-allowed">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span>{{ $post->likes->count() }} likes</span>
                        </button>
                    @endauth
                    <div class="flex items-center space-x-2 text-gray-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span>{{ $post->comments->count() }} comments</span>
                    </div>
                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span>Share</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Comments ({{ $post->comments->count() }})</h3>
                
                <!-- Add Comment Form -->
                @auth
                    <form action="{{ route('posts.comments.store', $post) }}" method="POST" class="mb-6">
                        @csrf
                        <div class="flex space-x-3">
                            <img class="h-10 w-10 rounded-full" src="{{ $post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ $post->user->name }}">
                            
                            <div class="flex-1">
                                <textarea name="content" rows="3" placeholder="Write a comment..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required></textarea>
                                <div class="mt-2 flex justify-end">
                                    <button type="submit" class="px-4 py-2 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                                        Post Comment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                @endauth

                <!-- Comments List -->
                <div class="space-y-4">
                    @forelse($post->comments as $comment)
                        <div class="flex space-x-3">
                            <img class="h-10 w-10 rounded-full" src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ $comment->user->name }}">
                            <div class="flex-1">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-gray-900">{{ $comment->user->name }}</span>
                                        <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                                    </div>
                                    <p class="text-gray-700">{!! nl2br(e($comment->content)) !!}</p>
                                </div>
                                
                                <!-- Comment Actions -->
                                <div class="flex items-center space-x-4 mt-2 text-sm">
                                    <button class="text-gray-500 hover:text-blue-600">Like</button>
                                    <button class="text-gray-500 hover:text-blue-600">Reply</button>
                                    @if(auth()->id() === $comment->user_id || auth()->user()->isAdmin())
                                        <button class="text-gray-500 hover:text-red-600">Delete</button>
                                    @endif
                                </div>

                                <!-- Replies -->
                                @if($comment->replies->count() > 0)
                                    <div class="mt-4 ml-4 space-y-3">
                                        @foreach($comment->replies as $reply)
                                            <div class="flex space-x-3">
                                                <img class="h-8 w-8 rounded-full" src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ $reply->user->name }}">
                                                <div class="flex-1">
                                                    <div class="bg-gray-50 rounded-lg p-3">
                                                        <div class="flex items-center space-x-2 mb-1">
                                                            <span class="font-medium text-gray-900">{{ $reply->user->name }}</span>
                                                            <span class="text-sm text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                                        </div>
                                                        <p class="text-gray-700">{!! nl2br(e($reply->content)) !!}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <script>
    // toggleLike function is now handled by comment-modal.js for consistency
    </script>
</x-unilink-layout>
