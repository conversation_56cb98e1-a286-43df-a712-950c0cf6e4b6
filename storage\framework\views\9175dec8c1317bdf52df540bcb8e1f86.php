<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['comment', 'post']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['comment', 'post']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150" data-comment-id="<?php echo e($comment->id); ?>">
    <div class="flex space-x-3">
        <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="flex-shrink-0">
            <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                 src="<?php echo e($comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                 alt="<?php echo e($comment->user->name); ?>">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-2 mb-2">
                    <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                        <?php echo e($comment->user->name); ?>

                    </a>
                    <span class="text-xs text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                    <?php if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin())): ?>
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editComment(<?php echo e($comment->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit</span>
                                </button>
                                <button onclick="deleteComment(<?php echo e($comment->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="comment-content">
                    <p class="text-gray-700 text-sm leading-relaxed"><?php echo nl2br(e($comment->content)); ?></p>
                </div>

                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-3">
                    <form class="edit-comment-form" data-comment-id="<?php echo e($comment->id); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <textarea name="content" rows="2"
                                  class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3"><?php echo e($comment->content); ?></textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment(<?php echo e($comment->id); ?>)"
                                    class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                            <button type="submit"
                                    class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                <?php if(auth()->guard()->check()): ?>
                    <div class="flex items-center space-x-2">
                        <?php if (isset($component)) { $__componentOriginalbb69de25e0b6990164a7273317cca957 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbb69de25e0b6990164a7273317cca957 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.reaction-picker','data' => ['targetId' => $comment->id,'targetType' => 'comment','currentReaction' => $comment->getUserReaction(auth()->user())]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('reaction-picker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['targetId' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment->id),'targetType' => 'comment','currentReaction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment->getUserReaction(auth()->user()))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbb69de25e0b6990164a7273317cca957)): ?>
<?php $attributes = $__attributesOriginalbb69de25e0b6990164a7273317cca957; ?>
<?php unset($__attributesOriginalbb69de25e0b6990164a7273317cca957); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbb69de25e0b6990164a7273317cca957)): ?>
<?php $component = $__componentOriginalbb69de25e0b6990164a7273317cca957; ?>
<?php unset($__componentOriginalbb69de25e0b6990164a7273317cca957); ?>
<?php endif; ?>
                        <span id="comment-reaction-count-<?php echo e($comment->id); ?>" class="text-xs text-gray-500">
                            <?php echo e($comment->likes->count() ?: ''); ?>

                        </span>
                    </div>
                <?php else: ?>
                    <span class="flex items-center space-x-1 text-gray-400">
                        <span class="text-sm">👍</span>
                        <span class="text-xs"><?php echo e($comment->likes->count()); ?></span>
                    </span>
                <?php endif; ?>

                <?php if(auth()->guard()->check()): ?>
                    <button onclick="showReplyForm(<?php echo e($comment->id); ?>)" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                        </svg>
                        <span class="font-medium">Reply</span>
                    </button>
                <?php endif; ?>

                <?php if($comment->created_at != $comment->updated_at): ?>
                    <span class="text-xs text-gray-400 italic">• edited</span>
                <?php endif; ?>
            </div>

            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3 ml-4" id="reply-form-<?php echo e($comment->id); ?>">
                <?php if(auth()->guard()->check()): ?>
                    <form class="comment-form" data-post-id="<?php echo e($post->id); ?>" data-parent-id="<?php echo e($comment->id); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                     src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                     alt="<?php echo e(auth()->user()->name); ?>">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="relative">
                                    <textarea name="content" rows="1"
                                              placeholder="Write a reply..."
                                              class="w-full px-3 py-2 border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200"
                                              required></textarea>
                                </div>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm(<?php echo e($comment->id); ?>)"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Replies -->
            <?php if($comment->replies->count() > 0): ?>
                <div class="nested-comments mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4">
                    <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if (isset($component)) { $__componentOriginald908f04fb1ba83f78e519c9f401232b9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald908f04fb1ba83f78e519c9f401232b9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment-item','data' => ['comment' => $reply,'post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reply),'post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $attributes = $__attributesOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $component = $__componentOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__componentOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/comment-item.blade.php ENDPATH**/ ?>