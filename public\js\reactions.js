// Reaction system JavaScript
let reactionPickerTimeout;
let currentPickerElement = null;

// Show reaction picker on hover
function showReactionPicker(button) {
    clearTimeout(reactionPickerTimeout);

    // Hide any other open pickers
    hideAllReactionPickers();

    const picker = button.nextElementSibling;
    if (picker && picker.classList.contains('reaction-picker')) {
        currentPickerElement = picker;

        // Show immediately for better responsiveness
        picker.classList.remove('opacity-0', 'invisible', 'scale-95');
        picker.classList.add('opacity-100', 'visible', 'scale-100');

        // Force a style recalculation to ensure visibility
        picker.style.display = 'block';
        picker.offsetHeight; // Trigger reflow
    }
}

// Hide reaction picker
function hideReactionPicker(button) {
    reactionPickerTimeout = setTimeout(() => {
        const picker = button.nextElementSibling;
        if (picker && picker.classList.contains('reaction-picker')) {
            picker.classList.add('opacity-0', 'invisible', 'scale-95');
            picker.classList.remove('opacity-100', 'visible', 'scale-100');
            currentPickerElement = null;
        }
    }, 200); // Small delay to allow moving to picker
}

// Keep picker visible when hovering over it
function keepReactionPickerVisible(picker) {
    clearTimeout(reactionPickerTimeout);
}

// Hide all reaction pickers
function hideAllReactionPickers() {
    clearTimeout(reactionPickerTimeout);
    const pickers = document.querySelectorAll('.reaction-picker');
    pickers.forEach(picker => {
        picker.classList.add('opacity-0', 'invisible', 'scale-95');
        picker.classList.remove('opacity-100', 'visible', 'scale-100');
    });
    currentPickerElement = null;
}

// Select a specific reaction
async function selectReaction(targetId, targetType, reactionType) {
    hideAllReactionPickers();

    try {
        // Get current reaction to check if we're clicking the same one
        const button = document.querySelector(`[data-target-id="${targetId}"][data-target-type="${targetType}"]`);
        const currentReaction = button?.dataset.currentReaction;

        // If clicking the same reaction, remove it
        const finalReactionType = (currentReaction === reactionType) ? null : reactionType;

        let url;
        switch(targetType) {
            case 'post':
                url = `/posts/${targetId}/react`;
                break;
            case 'comment':
                url = `/comments/${targetId}/react`;
                break;
            case 'share':
                url = `/shares/${targetId}/react`;
                break;
            default:
                throw new Error('Invalid target type');
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                reaction_type: finalReactionType
            })
        });

        const data = await response.json();

        if (data.success) {
            updateReactionButton(targetId, targetType, data);
            if (finalReactionType) {
                animateReaction(targetId, targetType, finalReactionType);
            }
            // Re-initialize reaction system to ensure event handlers work
            setTimeout(() => {
                initializeReactionSystem();
            }, 100);
        } else {
            console.error('Failed to update reaction:', data.message);
        }
    } catch (error) {
        console.error('Error selecting reaction:', error);
    }
}

// Toggle default reaction (like)
async function toggleReaction(targetId, targetType, defaultReaction = 'like') {
    const button = document.querySelector(`[data-target-id="${targetId}"][data-target-type="${targetType}"]`);
    const currentReaction = button?.dataset.currentReaction;
    
    if (currentReaction && currentReaction !== 'null') {
        // Remove current reaction
        await selectReaction(targetId, targetType, null);
    } else {
        // Add default reaction
        await selectReaction(targetId, targetType, defaultReaction);
    }
}

// Update reaction button UI
function updateReactionButton(targetId, targetType, data) {
    // Find all buttons for this target (there might be multiple instances)
    const buttons = document.querySelectorAll(`[data-target-id="${targetId}"][data-target-type="${targetType}"]`);

    const reactions = {
        'like': { emoji: '👍', label: 'Like', color: 'text-blue-600' },
        'love': { emoji: '❤️', label: 'Love', color: 'text-red-600' },
        'haha': { emoji: '😂', label: 'Haha', color: 'text-yellow-600' },
        'wow': { emoji: '😮', label: 'Wow', color: 'text-orange-600' },
        'sad': { emoji: '😢', label: 'Sad', color: 'text-blue-800' },
        'angry': { emoji: '😠', label: 'Angry', color: 'text-red-800' }
    };

    buttons.forEach(button => {
        // Store reference to the picker before updating button content
        const picker = button.nextElementSibling;
        const isReactionPicker = picker && picker.classList.contains('reaction-picker');

        // Update button content
        if (data.user_reaction) {
            const reaction = reactions[data.user_reaction.reaction_type];
            button.innerHTML = `
                <span class="text-lg">${reaction.emoji}</span>
                <span class="text-sm font-medium ${reaction.color}">${reaction.label}</span>
            `;
            button.dataset.currentReaction = data.user_reaction.reaction_type;
        } else {
            button.innerHTML = `
                <span class="text-lg">👍</span>
                <span class="text-sm font-medium">Like</span>
            `;
            button.dataset.currentReaction = '';
        }

        // Update the reaction picker to highlight current reaction
        if (isReactionPicker) {
            updateReactionPickerHighlight(button, data.user_reaction?.reaction_type);
        }
    });

    // Update reaction counts if element exists
    updateReactionCounts(targetId, targetType, data.reaction_counts);
}

// Update reaction picker highlight
function updateReactionPickerHighlight(button, currentReactionType) {
    const picker = button.nextElementSibling;
    if (picker && picker.classList.contains('reaction-picker')) {
        const options = picker.querySelectorAll('.reaction-option');
        options.forEach(option => {
            const reactionType = option.dataset.reactionType;
            if (reactionType === currentReactionType) {
                option.classList.add('ring-2', 'ring-blue-500', 'scale-110');
            } else {
                option.classList.remove('ring-2', 'ring-blue-500', 'scale-110');
            }
        });
    }
}

// Update reaction counts display
function updateReactionCounts(targetId, targetType, counts) {
    // Try different possible count element IDs
    const possibleIds = [
        `${targetType}-reaction-count-${targetId}`,
        `${targetType}-like-count-${targetId}`,
        `like-count-${targetId}`,
        `comment-reaction-count-${targetId}`,
        `share-reaction-count-${targetId}`
    ];

    let countElement = null;
    for (const id of possibleIds) {
        countElement = document.getElementById(id);
        if (countElement) break;
    }

    if (countElement && counts) {
        const totalCount = Object.values(counts).reduce((sum, count) => sum + count, 0);
        if (targetType === 'comment') {
            countElement.textContent = totalCount > 0 ? totalCount : '';
        } else {
            countElement.textContent = totalCount > 0 ? `${totalCount} reactions` : '';
        }

        // Update individual reaction counts if they exist
        Object.entries(counts).forEach(([reactionType, count]) => {
            const specificCountElement = document.getElementById(`${targetType}-${reactionType}-count-${targetId}`);
            if (specificCountElement) {
                specificCountElement.textContent = count > 0 ? count : '';
            }
        });
    }
}

// Animate reaction
function animateReaction(targetId, targetType, reactionType) {
    const button = document.querySelector(`[data-target-id="${targetId}"][data-target-type="${targetType}"]`);
    if (!button) return;

    const animations = {
        'like': 'bounce',
        'love': 'heartBeat',
        'haha': 'shake',
        'wow': 'pulse',
        'sad': 'fadeIn',
        'angry': 'shake'
    };

    const animationClass = `reaction-animation-${animations[reactionType] || 'bounce'}`;
    
    // Add animation class
    button.classList.add(animationClass);
    
    // Remove animation class after animation completes
    setTimeout(() => {
        button.classList.remove(animationClass);
    }, 600);
}

// Initialize reaction system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeReactionSystem();
});

// Initialize reaction system for dynamically loaded content
function initializeReactionSystem() {
    console.log('Initializing reaction system...');

    // Attach event listeners to all reaction buttons
    const reactionButtons = document.querySelectorAll('.reaction-main-btn');
    console.log('Found reaction buttons:', reactionButtons.length);

    reactionButtons.forEach((button, index) => {
        console.log(`Initializing button ${index}:`, button);

        // Remove existing listeners first
        button.removeEventListener('mouseenter', handleMouseEnter);
        button.removeEventListener('mouseleave', handleMouseLeave);
        button.removeEventListener('click', handleClick);

        // Add fresh event listeners
        button.addEventListener('mouseenter', handleMouseEnter, true);
        button.addEventListener('mouseleave', handleMouseLeave, true);
        button.addEventListener('click', handleClick, true);
    });

    // Attach event listeners to reaction pickers
    const reactionPickers = document.querySelectorAll('.reaction-picker');
    reactionPickers.forEach(picker => {
        picker.removeEventListener('mouseenter', handlePickerMouseEnter);
        picker.removeEventListener('mouseleave', handlePickerMouseLeave);

        picker.addEventListener('mouseenter', handlePickerMouseEnter);
        picker.addEventListener('mouseleave', handlePickerMouseLeave);
    });

    // Attach event listeners to reaction options
    const reactionOptions = document.querySelectorAll('.reaction-option');
    reactionOptions.forEach(option => {
        option.removeEventListener('click', handleReactionOptionClick);
        option.addEventListener('click', handleReactionOptionClick);
    });
}

// Event handlers
function handleMouseEnter(event) {
    showReactionPicker(event.currentTarget);
}

function handleMouseLeave(event) {
    hideReactionPicker(event.currentTarget);
}

function handleClick(event) {
    event.preventDefault();
    const button = event.currentTarget;
    const targetId = button.dataset.targetId;
    const targetType = button.dataset.targetType;
    const defaultReaction = 'like';

    toggleReaction(targetId, targetType, defaultReaction);
}

function handlePickerMouseEnter(event) {
    clearTimeout(reactionPickerTimeout);
}

function handlePickerMouseLeave(event) {
    const picker = event.currentTarget;
    const button = picker.previousElementSibling;
    if (button && button.classList.contains('reaction-main-btn')) {
        hideReactionPicker(button);
    }
}

function handleReactionOptionClick(event) {
    event.preventDefault();
    const option = event.currentTarget;
    const targetId = option.dataset.targetId;
    const targetType = option.dataset.targetType;
    const reactionType = option.dataset.reactionType;

    selectReaction(targetId, targetType, reactionType);
}

// Close reaction picker when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.reaction-picker') && !event.target.closest('.reaction-main-btn')) {
        hideAllReactionPickers();
    }
});

// Legacy support for existing toggleLike function
window.toggleLike = function(postId) {
    return toggleReaction(postId, 'post', 'like');
};

window.toggleCommentLike = function(commentId) {
    return toggleReaction(commentId, 'comment', 'like');
};

window.toggleShareLike = function(shareId) {
    return toggleReaction(shareId, 'share', 'like');
};

// Export function for dynamic content
window.initializeReactionSystem = initializeReactionSystem;
