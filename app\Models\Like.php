<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Like extends Model
{
    protected $fillable = [
        'likeable_type',
        'likeable_id',
        'user_id',
        'reaction_type',
    ];

    /**
     * Get the likeable model (post, comment, etc.)
     */
    public function likeable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who made this like
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get available reaction types
     */
    public static function getReactionTypes(): array
    {
        return config('reactions.types', []);
    }

    /**
     * Get reaction emoji
     */
    public function getEmojiAttribute(): string
    {
        $reactions = self::getReactionTypes();
        return $reactions[$this->reaction_type]['emoji'] ?? '👍';
    }

    /**
     * Get reaction label
     */
    public function getLabelAttribute(): string
    {
        $reactions = self::getReactionTypes();
        return $reactions[$this->reaction_type]['label'] ?? 'Like';
    }

    /**
     * Get reaction color class
     */
    public function getColorAttribute(): string
    {
        $reactions = self::getReactionTypes();
        return $reactions[$this->reaction_type]['color'] ?? 'text-blue-600';
    }
}
