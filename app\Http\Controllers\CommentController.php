<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * Store a newly created comment.
     */
    public function store(Request $request, Post $post)
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['commentable_type'] = Post::class;
        $validated['commentable_id'] = $post->id;

        $comment = Comment::create($validated);
        $comment->load('user', 'likes');

        // Add like status for current user
        $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
        $comment->likes_count = $comment->likes()->count();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'message' => 'Comment added successfully!'
            ]);
        }

        return back()->with('success', 'Comment added successfully!');
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment)
    {
        // Check if user can edit this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $comment->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'message' => 'Comment updated successfully!'
            ]);
        }

        return back()->with('success', 'Comment updated successfully!');
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment)
    {
        // Check if user can delete this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $comment->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment deleted successfully!'
            ]);
        }

        return back()->with('success', 'Comment deleted successfully!');
    }

    /**
     * Get comments for a post with sorting
     */
    public function index(Request $request, Post $post)
    {
        $sortBy = $request->get('sort', 'most_relevant');

        $query = $post->comments()
            ->with(['user', 'likes', 'replies' => function ($query) use ($sortBy) {
                $this->applySorting($query, $sortBy);
                $query->with('user', 'likes');
            }])
            ->whereNull('parent_id');

        $this->applySorting($query, $sortBy);

        $comments = $query->get();

        // Add like status and counts for current user
        $comments->each(function ($comment) {
            $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
            $comment->likes_count = $comment->likes()->count();

            // Also add for replies
            $comment->replies->each(function ($reply) {
                $reply->is_liked_by_user = $reply->isLikedBy(auth()->user());
                $reply->likes_count = $reply->likes()->count();
            });
        });

        return response()->json([
            'success' => true,
            'comments' => $comments,
            'sort' => $sortBy
        ]);
    }

    /**
     * Apply sorting to comment query
     */
    private function applySorting($query, $sortBy)
    {
        switch ($sortBy) {
            case 'newest':
                $query->latest('created_at');
                break;
            case 'oldest':
                $query->oldest('created_at');
                break;
            case 'most_relevant':
            default:
                // Most relevant: sort by likes count desc, then by newest
                $query->withCount('likes')
                      ->orderByDesc('likes_count')
                      ->latest('created_at');
                break;
        }
    }

    /**
     * Toggle like on a comment
     */
    public function toggleLike(Comment $comment)
    {
        $user = auth()->user();
        $like = $comment->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $liked = false;
        } else {
            $comment->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $comment->likes()->count()
        ]);
    }

    /**
     * Add or update reaction on a comment
     */
    public function react(Request $request, Comment $comment)
    {
        $request->validate([
            'reaction_type' => 'nullable|string|in:like,love,haha,wow,sad,angry'
        ]);

        $user = auth()->user();
        $existingReaction = $comment->likes()->where('user_id', $user->id)->first();

        if ($request->reaction_type === null) {
            // Remove reaction
            if ($existingReaction) {
                $existingReaction->delete();
            }
            $userReaction = null;
        } else {
            // Add or update reaction
            if ($existingReaction) {
                $existingReaction->update(['reaction_type' => $request->reaction_type]);
                $userReaction = $existingReaction->fresh();
            } else {
                $userReaction = $comment->likes()->create([
                    'user_id' => $user->id,
                    'reaction_type' => $request->reaction_type
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'user_reaction' => $userReaction,
            'reaction_counts' => $comment->getReactionCounts(),
            'total_reactions' => $comment->likes()->count()
        ]);
    }
}
