<?php

return [
    'types' => [
        'like' => [
            'emoji' => '👍',
            'label' => 'Like',
            'color' => 'text-blue-600',
            'hover_color' => 'hover:text-blue-600',
            'bg_color' => 'bg-blue-50',
            'animation' => 'bounce'
        ],
        'love' => [
            'emoji' => '❤️',
            'label' => 'Love',
            'color' => 'text-red-600',
            'hover_color' => 'hover:text-red-600',
            'bg_color' => 'bg-red-50',
            'animation' => 'heartBeat'
        ],
        'haha' => [
            'emoji' => '😂',
            'label' => 'Haha',
            'color' => 'text-yellow-600',
            'hover_color' => 'hover:text-yellow-600',
            'bg_color' => 'bg-yellow-50',
            'animation' => 'shake'
        ],
        'wow' => [
            'emoji' => '😮',
            'label' => 'Wow',
            'color' => 'text-orange-600',
            'hover_color' => 'hover:text-orange-600',
            'bg_color' => 'bg-orange-50',
            'animation' => 'pulse'
        ],
        'sad' => [
            'emoji' => '😢',
            'label' => 'Sad',
            'color' => 'text-blue-800',
            'hover_color' => 'hover:text-blue-800',
            'bg_color' => 'bg-blue-50',
            'animation' => 'fadeIn'
        ],
        'angry' => [
            'emoji' => '😠',
            'label' => 'Angry',
            'color' => 'text-red-800',
            'hover_color' => 'hover:text-red-800',
            'bg_color' => 'bg-red-50',
            'animation' => 'shake'
        ]
    ],
    
    'default' => 'like',
    
    'picker_delay' => 500, // milliseconds to show picker on hover
    'animation_duration' => 600 // milliseconds for reaction animations
];
